{
  "compilerOptions": {
    "target": "ES6",
    "module": "ESNext",
    "declaration": true,
    "lib": [
      "ES6",
      "DOM"
    ],
    "moduleResolution": "node",
    "outDir": "dist",
    "strict": false,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "emitDeclarationOnly": true,
    "forceConsistentCasingInFileNames": true,
  },
  "files": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.node.json"
    }
  ]
}