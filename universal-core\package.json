{"name": "mg-universal-core", "version": "0.0.1", "type": "module", "main": "dist/mg-universal-core.js", "types": "dist/index.d.ts", "exports": {".": {"import": "dist/mg-universal-core.js", "types": "dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^24.0.1", "rollup-plugin-peer-deps-external": "^2.2.4", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.4"}, "dependencies": {}, "peerDependencies": {"three": "^0.177.0", "@types/three": "^0.177.0"}}