<template>
    <div class="scene-viewer">
        <canvas ref="sceneViewCanvas" class="scene-view-canvas"></canvas>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import {Application} from "../application";

const sceneViewCanvas = ref(null);
const emit = defineEmits(['engine-init', 'model-loaded', 'animation-loaded', 'error']);
let application = null;

onMounted(async () => {
    try {
        // Wait for next tick to ensure canvas is properly sized
        await new Promise(resolve => setTimeout(resolve, 100));

        const canvas = sceneViewCanvas.value;
        const container = canvas.parentElement;

        // Force canvas to match container size
        const containerRect = container.getBoundingClientRect();
        console.log('Container dimensions:', {
            width: containerRect.width,
            height: containerRect.height
        });

        // Set canvas size explicitly
        canvas.style.width = '100%';
        canvas.style.height = '100%';

        // Debug: Check canvas size after setting
        console.log('Canvas dimensions after setting:', {
            clientWidth: canvas.clientWidth,
            clientHeight: canvas.clientHeight,
            offsetWidth: canvas.offsetWidth,
            offsetHeight: canvas.offsetHeight
        });

        application = new Application();
        container.appendChild(application.getDomElement());

        application.run();
    } catch (error) {
        console.error("init engine error:", error);
    }
});

onUnmounted(() => {
    //application?.destroy();
});
</script>

<style scoped>
.avatar-viewer {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 500px; /* 确保有足够的高度 */
    overflow: hidden;
    box-sizing: border-box;
}

.avatar-view-canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
    box-sizing: border-box;
}
</style>
