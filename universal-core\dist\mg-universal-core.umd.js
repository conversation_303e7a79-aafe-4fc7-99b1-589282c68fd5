(function(e,t){typeof exports=="object"&&typeof module<"u"?t(exports,require("three")):typeof define=="function"&&define.amd?define(["exports","three"],t):(e=typeof globalThis<"u"?globalThis:e||self,t(e.MGUniversalCore={},e.THREE))})(this,function(e,t){"use strict";function u(n){const o=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(n){for(const r in n)if(r!=="default"){const i=Object.getOwnPropertyDescriptor(n,r);Object.defineProperty(o,r,i.get?i:{enumerable:!0,get:()=>n[r]})}}return o.default=n,Object.freeze(o)}const c=u(t);class s{constructor(){}init(){return console.log("Initializing Engine"),!0}destroy(){console.log("Destroying Engine")}createRenderer(){return new c.WebGLRenderer}}e.Engine=s,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})});
