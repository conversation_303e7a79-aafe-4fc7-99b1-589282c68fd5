<script setup lang="ts">
import SceneView from './components/SceneView.vue'
</script>

<template>
  <div id="app">
    <div class="app-header">
      <h2 style="margin: 0; font-size: 1.25rem;">Universal space SandBox</h2>
    </div>
    <SceneView class="viewer-container"></SceneView>
  </div>
</template>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
