{"name": "sandbox", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@types/three": "^0.177.0", "mg-universal-core": "../universal-core", "vue": "^3.5.13"}, "devDependencies": {"@types/three": "^0.177.0", "@types/node": "^24.0.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}