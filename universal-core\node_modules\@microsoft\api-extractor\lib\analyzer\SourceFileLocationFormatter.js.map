{"version": 3, "file": "SourceFileLocationFormatter.js", "sourceRoot": "", "sources": ["../../src/analyzer/SourceFileLocationFormatter.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG3D,2CAA6B;AAC7B,oEAA0D;AAQ1D,MAAa,2BAA2B;IACtC;;;OAGG;IACI,MAAM,CAAC,iBAAiB,CAAC,IAAa,EAAE,wBAAiC;QAC9E,MAAM,UAAU,GAAkB,IAAI,CAAC,aAAa,EAAE,CAAC;QACvD,MAAM,gBAAgB,GAAwB,UAAU,CAAC,6BAA6B,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAExG,OAAO,2BAA2B,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE;YACjE,cAAc,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC;YACzC,gBAAgB,EAAE,gBAAgB,CAAC,SAAS,GAAG,CAAC;YAChD,wBAAwB;SACzB,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,UAAU,CAAC,cAAsB,EAAE,OAA0C;QACzF,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QAED,IAAI,MAAM,GAAW,EAAE,CAAC;QAExB,yDAAyD;QACzD,IAAI,YAAY,GAAW,cAAc,CAAC;QAE1C,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;YACrC,4DAA4D;YAC5D,IAAI,wBAAI,CAAC,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC,wBAAwB,CAAC,EAAE,CAAC;gBAC1E,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC;YACjF,CAAC;QACH,CAAC;QAED,kCAAkC;QAClC,YAAY,GAAG,wBAAI,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QACxD,MAAM,IAAI,YAAY,CAAC;QAEvB,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,MAAM,IAAI,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAEvC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC7B,MAAM,IAAI,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA/CD,kEA+CC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport type * as ts from 'typescript';\nimport * as path from 'path';\nimport { Path, Text } from '@rushstack/node-core-library';\n\nexport interface ISourceFileLocationFormatOptions {\n  sourceFileLine?: number;\n  sourceFileColumn?: number;\n  workingPackageFolderPath?: string;\n}\n\nexport class SourceFileLocationFormatter {\n  /**\n   * Returns a string such as this, based on the context information in the provided node:\n   *   \"[C:\\Folder\\File.ts#123]\"\n   */\n  public static formatDeclaration(node: ts.Node, workingPackageFolderPath?: string): string {\n    const sourceFile: ts.SourceFile = node.getSourceFile();\n    const lineAndCharacter: ts.LineAndCharacter = sourceFile.getLineAndCharacterOfPosition(node.getStart());\n\n    return SourceFileLocationFormatter.formatPath(sourceFile.fileName, {\n      sourceFileLine: lineAndCharacter.line + 1,\n      sourceFileColumn: lineAndCharacter.character + 1,\n      workingPackageFolderPath\n    });\n  }\n\n  public static formatPath(sourceFilePath: string, options?: ISourceFileLocationFormatOptions): string {\n    if (!options) {\n      options = {};\n    }\n\n    let result: string = '';\n\n    // Make the path relative to the workingPackageFolderPath\n    let scrubbedPath: string = sourceFilePath;\n\n    if (options.workingPackageFolderPath) {\n      // If it's under the working folder, make it a relative path\n      if (Path.isUnderOrEqual(sourceFilePath, options.workingPackageFolderPath)) {\n        scrubbedPath = path.relative(options.workingPackageFolderPath, sourceFilePath);\n      }\n    }\n\n    // Convert it to a Unix-style path\n    scrubbedPath = Text.replaceAll(scrubbedPath, '\\\\', '/');\n    result += scrubbedPath;\n\n    if (options.sourceFileLine) {\n      result += `:${options.sourceFileLine}`;\n\n      if (options.sourceFileColumn) {\n        result += `:${options.sourceFileColumn}`;\n      }\n    }\n\n    return result;\n  }\n}\n"]}