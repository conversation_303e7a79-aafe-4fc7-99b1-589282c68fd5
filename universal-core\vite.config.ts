import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import { resolve } from 'path';

export default defineConfig({
    plugins: [
        dts({
            insertTypesEntry: true, // 生成类型声明入口
        })
    ],
    build: {
        lib: {
            entry: resolve(__dirname, 'src/index.ts'),   // 入口文件
            fileName: 'mg-universal-core',
            formats: ['es'],
        },
        rollupOptions: {
            // 确保 three 不会被打包进库中
            external: [
                'three'
            ]
        }
    }
});