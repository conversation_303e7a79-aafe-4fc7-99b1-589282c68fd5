/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{TSL as e}from"three/webgpu";const t=e.BRDF_GGX,r=e.BRDF_Lambert,a=e.BasicShadowFilter,o=e.Break,i=e.Continue,n=e.DFGApprox,s=e.D_GGX,l=e.Discard,c=e.EPSILON,m=e.F_Schlick,p=e.Fn,d=e.INFINITY,u=e.If,g=e.Switch,h=e.Loop,f=e.NodeShaderStage,x=e.NodeType,b=e.NodeUpdateType,w=e.NodeAccess,v=e.PCFShadowFilter,S=e.PCFSoftShadowFilter,T=e.PI,_=e.PI2,V=e.Return,y=e.Schlick_to_F0,M=e.ScriptableNodeResources,D=e.ShaderNode,F=e.TBNViewMatrix,C=e.VSMShadowFilter,I=e.V_GGX_SmithCorrelated,P=e.abs,R=e.acesFilmicToneMapping,N=e.acos,B=e.add,L=e.addNodeElement,k=e.agxToneMapping,A=e.all,G=e.alphaT,O=e.and,W=e.anisotropy,j=e.anisotropyB,U=e.anisotropyT,z=e.any,q=e.append,E=e.array,Z=e.arrayBuffer,X=e.asin,Y=e.assign,H=e.atan,J=e.atan2,K=e.atomicAdd,Q=e.atomicAnd,$=e.atomicFunc,ee=e.atomicMax,te=e.atomicMin,re=e.atomicOr,ae=e.atomicStore,oe=e.atomicSub,ie=e.atomicXor,ne=e.atomicLoad,se=e.attenuationColor,le=e.attenuationDistance,ce=e.attribute,me=e.attributeArray,pe=e.backgroundBlurriness,de=e.backgroundIntensity,ue=e.backgroundRotation,ge=e.batch,he=e.billboarding,fe=e.bitAnd,xe=e.bitNot,be=e.bitOr,we=e.bitXor,ve=e.bitangentGeometry,Se=e.bitangentLocal,Te=e.bitangentView,_e=e.bitangentWorld,Ve=e.bitcast,ye=e.blendBurn,Me=e.blendColor,De=e.blendDodge,Fe=e.blendOverlay,Ce=e.blendScreen,Ie=e.blur,Pe=e.bool,Re=e.buffer,Ne=e.bufferAttribute,Be=e.bumpMap,Le=e.burn,ke=e.bvec2,Ae=e.bvec3,Ge=e.bvec4,Oe=e.bypass,We=e.cache,je=e.call,Ue=e.cameraFar,ze=e.cameraIndex,qe=e.cameraNear,Ee=e.cameraNormalMatrix,Ze=e.cameraPosition,Xe=e.cameraProjectionMatrix,Ye=e.cameraProjectionMatrixInverse,He=e.cameraViewMatrix,Je=e.cameraWorldMatrix,Ke=e.cbrt,Qe=e.cdl,$e=e.ceil,et=e.checker,tt=e.cineonToneMapping,rt=e.clamp,at=e.clearcoat,ot=e.clearcoatRoughness,it=e.code,nt=e.color,st=e.colorSpaceToWorking,lt=e.colorToDirection,ct=e.compute,mt=e.computeSkinning,pt=e.cond,dt=e.Const,ut=e.context,gt=e.convert,ht=e.convertColorSpace,ft=e.convertToTexture,xt=e.cos,bt=e.cross,wt=e.cubeTexture,vt=e.dFdx,St=e.dFdy,Tt=e.dashSize,_t=e.debug,Vt=e.decrement,yt=e.decrementBefore,Mt=e.defaultBuildStages,Dt=e.defaultShaderStages,Ft=e.defined,Ct=e.degrees,It=e.deltaTime,Pt=e.densityFog,Rt=e.densityFogFactor,Nt=e.depth,Bt=e.depthPass,Lt=e.difference,kt=e.diffuseColor,At=e.directPointLight,Gt=e.directionToColor,Ot=e.dispersion,Wt=e.distance,jt=e.div,Ut=e.dodge,zt=e.dot,qt=e.drawIndex,Et=e.dynamicBufferAttribute,Zt=e.element,Xt=e.emissive,Yt=e.equal,Ht=e.equals,Jt=e.equirectUV,Kt=e.exp,Qt=e.exp2,$t=e.expression,er=e.faceDirection,tr=e.faceForward,rr=e.faceforward,ar=e.float,or=e.floor,ir=e.fog,nr=e.fract,sr=e.frameGroup,lr=e.frameId,cr=e.frontFacing,mr=e.fwidth,pr=e.gain,dr=e.gapSize,ur=e.getConstNodeType,gr=e.getCurrentStack,hr=e.getDirection,fr=e.getDistanceAttenuation,xr=e.getGeometryRoughness,br=e.getNormalFromDepth,wr=e.getParallaxCorrectNormal,vr=e.getRoughness,Sr=e.getScreenPosition,Tr=e.getShIrradianceAt,_r=e.getTextureIndex,Vr=e.getViewPosition,yr=e.getShadowMaterial,Mr=e.getShadowRenderObjectFunction,Dr=e.glsl,Fr=e.glslFn,Cr=e.grayscale,Ir=e.greaterThan,Pr=e.greaterThanEqual,Rr=e.hash,Nr=e.highpModelNormalViewMatrix,Br=e.highpModelViewMatrix,Lr=e.hue,kr=e.increment,Ar=e.incrementBefore,Gr=e.instance,Or=e.instanceIndex,Wr=e.instancedArray,jr=e.instancedBufferAttribute,Ur=e.instancedDynamicBufferAttribute,zr=e.instancedMesh,qr=e.int,Er=e.inverseSqrt,Zr=e.inversesqrt,Xr=e.invocationLocalIndex,Yr=e.invocationSubgroupIndex,Hr=e.ior,Jr=e.iridescence,Kr=e.iridescenceIOR,Qr=e.iridescenceThickness,$r=e.ivec2,ea=e.ivec3,ta=e.ivec4,ra=e.js,aa=e.label,oa=e.length,ia=e.lengthSq,na=e.lessThan,sa=e.lessThanEqual,la=e.lightPosition,ca=e.lightShadowMatrix,ma=e.lightTargetDirection,pa=e.lightTargetPosition,da=e.lightViewPosition,ua=e.lightingContext,ga=e.lights,ha=e.linearDepth,fa=e.linearToneMapping,xa=e.localId,ba=e.globalId,wa=e.log,va=e.log2,Sa=e.logarithmicDepthToViewZ,Ta=e.loop,_a=e.luminance,Va=e.mediumpModelViewMatrix,ya=e.mat2,Ma=e.mat3,Da=e.mat4,Fa=e.matcapUV,Ca=e.materialAO,Ia=e.materialAlphaTest,Pa=e.materialAnisotropy,Ra=e.materialAnisotropyVector,Na=e.materialAttenuationColor,Ba=e.materialAttenuationDistance,La=e.materialClearcoat,ka=e.materialClearcoatNormal,Aa=e.materialClearcoatRoughness,Ga=e.materialColor,Oa=e.materialDispersion,Wa=e.materialEmissive,ja=e.materialIOR,Ua=e.materialIridescence,za=e.materialIridescenceIOR,qa=e.materialIridescenceThickness,Ea=e.materialLightMap,Za=e.materialLineDashOffset,Xa=e.materialLineDashSize,Ya=e.materialLineGapSize,Ha=e.materialLineScale,Ja=e.materialLineWidth,Ka=e.materialMetalness,Qa=e.materialNormal,$a=e.materialOpacity,eo=e.materialPointSize,to=e.materialReference,ro=e.materialReflectivity,ao=e.materialRefractionRatio,oo=e.materialRotation,io=e.materialRoughness,no=e.materialSheen,so=e.materialSheenRoughness,lo=e.materialShininess,co=e.materialSpecular,mo=e.materialSpecularColor,po=e.materialSpecularIntensity,uo=e.materialSpecularStrength,go=e.materialThickness,ho=e.materialTransmission,fo=e.max,xo=e.maxMipLevel,bo=e.metalness,wo=e.min,vo=e.mix,So=e.mixElement,To=e.mod,_o=e.modInt,Vo=e.modelDirection,yo=e.modelNormalMatrix,Mo=e.modelPosition,Do=e.modelRadius,Fo=e.modelScale,Co=e.modelViewMatrix,Io=e.modelViewPosition,Po=e.modelViewProjection,Ro=e.modelWorldMatrix,No=e.modelWorldMatrixInverse,Bo=e.morphReference,Lo=e.mrt,ko=e.mul,Ao=e.mx_aastep,Go=e.mx_cell_noise_float,Oo=e.mx_contrast,Wo=e.mx_fractal_noise_float,jo=e.mx_fractal_noise_vec2,Uo=e.mx_fractal_noise_vec3,zo=e.mx_fractal_noise_vec4,qo=e.mx_hsvtorgb,Eo=e.mx_noise_float,Zo=e.mx_noise_vec3,Xo=e.mx_noise_vec4,Yo=e.mx_ramplr,Ho=e.mx_ramptb,Jo=e.mx_rgbtohsv,Ko=e.mx_safepower,Qo=e.mx_splitlr,$o=e.mx_splittb,ei=e.mx_srgb_texture_to_lin_rec709,ti=e.mx_transform_uv,ri=e.mx_worley_noise_float,ai=e.mx_worley_noise_vec2,oi=e.mx_worley_noise_vec3,ii=e.namespace,ni=e.negate,si=e.neutralToneMapping,li=e.nodeArray,ci=e.nodeImmutable,mi=e.nodeObject,pi=e.nodeObjects,di=e.nodeProxy,ui=e.normalFlat,gi=e.normalGeometry,hi=e.normalLocal,fi=e.normalMap,xi=e.normalView,bi=e.normalWorld,wi=e.normalize,vi=e.not,Si=e.notEqual,Ti=e.numWorkgroups,_i=e.objectDirection,Vi=e.objectGroup,yi=e.objectPosition,Mi=e.objectRadius,Di=e.objectScale,Fi=e.objectViewPosition,Ci=e.objectWorldMatrix,Ii=e.oneMinus,Pi=e.or,Ri=e.orthographicDepthToViewZ,Ni=e.oscSawtooth,Bi=e.oscSine,Li=e.oscSquare,ki=e.oscTriangle,Ai=e.output,Gi=e.outputStruct,Oi=e.overlay,Wi=e.overloadingFn,ji=e.parabola,Ui=e.parallaxDirection,zi=e.parallaxUV,qi=e.parameter,Ei=e.pass,Zi=e.passTexture,Xi=e.pcurve,Yi=e.perspectiveDepthToViewZ,Hi=e.pmremTexture,Ji=e.pointUV,Ki=e.pointWidth,Qi=e.positionGeometry,$i=e.positionLocal,en=e.positionPrevious,tn=e.positionView,rn=e.positionViewDirection,an=e.positionWorld,on=e.positionWorldDirection,nn=e.posterize,sn=e.pow,ln=e.pow2,cn=e.pow3,mn=e.pow4,pn=e.premult,dn=e.property,un=e.radians,gn=e.rand,hn=e.range,fn=e.rangeFog,xn=e.rangeFogFactor,bn=e.reciprocal,wn=e.lightProjectionUV,vn=e.reference,Sn=e.referenceBuffer,Tn=e.reflect,_n=e.reflectVector,Vn=e.reflectView,yn=e.reflector,Mn=e.refract,Dn=e.refractVector,Fn=e.refractView,Cn=e.reinhardToneMapping,In=e.remainder,Pn=e.remap,Rn=e.remapClamp,Nn=e.renderGroup,Bn=e.renderOutput,Ln=e.rendererReference,kn=e.rotate,An=e.rotateUV,Gn=e.roughness,On=e.round,Wn=e.rtt,jn=e.sRGBTransferEOTF,Un=e.sRGBTransferOETF,zn=e.sampler,qn=e.samplerComparison,En=e.saturate,Zn=e.saturation,Xn=e.screen,Yn=e.screenCoordinate,Hn=e.screenSize,Jn=e.screenUV,Kn=e.scriptable,Qn=e.scriptableValue,$n=e.select,es=e.setCurrentStack,ts=e.shaderStages,rs=e.shadow,as=e.pointShadow,os=e.shadowPositionWorld,is=e.sharedUniformGroup,ns=e.shapeCircle,ss=e.sheen,ls=e.sheenRoughness,cs=e.shiftLeft,ms=e.shiftRight,ps=e.shininess,ds=e.sign,us=e.sin,gs=e.sinc,hs=e.skinning,fs=e.smoothstep,xs=e.smoothstepElement,bs=e.specularColor,ws=e.specularF90,vs=e.spherizeUV,Ss=e.split,Ts=e.spritesheetUV,_s=e.sqrt,Vs=e.stack,ys=e.step,Ms=e.storage,Ds=e.storageBarrier,Fs=e.storageObject,Cs=e.storageTexture,Is=e.string,Ps=e.struct,Rs=e.sub,Ns=e.subgroupIndex,Bs=e.subgroupSize,Ls=e.tan,ks=e.tangentGeometry,As=e.tangentLocal,Gs=e.tangentView,Os=e.tangentWorld,Ws=e.temp,js=e.texture,Us=e.texture3D,zs=e.textureBarrier,qs=e.textureBicubic,Es=e.textureCubeUV,Zs=e.textureLoad,Xs=e.textureSize,Ys=e.textureStore,Hs=e.thickness,Js=e.time,Ks=e.timerDelta,Qs=e.timerGlobal,$s=e.timerLocal,el=e.toneMapping,tl=e.toneMappingExposure,rl=e.toonOutlinePass,al=e.transformDirection,ol=e.transformNormal,il=e.transformNormalToView,nl=e.transformedBentNormalView,sl=e.transformedBitangentView,ll=e.transformedBitangentWorld,cl=e.transformedClearcoatNormalView,ml=e.transformedNormalView,pl=e.transformedNormalWorld,dl=e.transformedTangentView,ul=e.transformedTangentWorld,gl=e.transmission,hl=e.transpose,fl=e.triNoise3D,xl=e.triplanarTexture,bl=e.triplanarTextures,wl=e.trunc,vl=e.tslFn,Sl=e.uint,Tl=e.uniform,_l=e.uniformCubeTexture,Vl=e.uniformArray,yl=e.uniformGroup,Ml=e.uniformTexture,Dl=e.uniforms,Fl=e.unpremult,Cl=e.userData,Il=e.uv,Pl=e.uvec2,Rl=e.uvec3,Nl=e.uvec4,Bl=e.Var,Ll=e.varying,kl=e.varyingProperty,Al=e.vec2,Gl=e.vec3,Ol=e.vec4,Wl=e.vectorComponents,jl=e.velocity,Ul=e.vertexColor,zl=e.vertexIndex,ql=e.vibrance,El=e.viewZToLogarithmicDepth,Zl=e.viewZToOrthographicDepth,Xl=e.viewZToPerspectiveDepth,Yl=e.viewport,Hl=e.viewportBottomLeft,Jl=e.viewportCoordinate,Kl=e.viewportDepthTexture,Ql=e.viewportLinearDepth,$l=e.viewportMipTexture,ec=e.viewportResolution,tc=e.viewportSafeUV,rc=e.viewportSharedTexture,ac=e.viewportSize,oc=e.viewportTexture,ic=e.viewportTopLeft,nc=e.viewportUV,sc=e.wgsl,lc=e.wgslFn,cc=e.workgroupArray,mc=e.workgroupBarrier,pc=e.workgroupId,dc=e.workingToColorSpace,uc=e.xor;export{t as BRDF_GGX,r as BRDF_Lambert,a as BasicShadowFilter,o as Break,dt as Const,i as Continue,n as DFGApprox,s as D_GGX,l as Discard,c as EPSILON,m as F_Schlick,p as Fn,d as INFINITY,u as If,h as Loop,w as NodeAccess,f as NodeShaderStage,x as NodeType,b as NodeUpdateType,v as PCFShadowFilter,S as PCFSoftShadowFilter,T as PI,_ as PI2,V as Return,y as Schlick_to_F0,M as ScriptableNodeResources,D as ShaderNode,g as Switch,F as TBNViewMatrix,C as VSMShadowFilter,I as V_GGX_SmithCorrelated,Bl as Var,P as abs,R as acesFilmicToneMapping,N as acos,B as add,L as addNodeElement,k as agxToneMapping,A as all,G as alphaT,O as and,W as anisotropy,j as anisotropyB,U as anisotropyT,z as any,q as append,E as array,Z as arrayBuffer,X as asin,Y as assign,H as atan,J as atan2,K as atomicAdd,Q as atomicAnd,$ as atomicFunc,ne as atomicLoad,ee as atomicMax,te as atomicMin,re as atomicOr,ae as atomicStore,oe as atomicSub,ie as atomicXor,se as attenuationColor,le as attenuationDistance,ce as attribute,me as attributeArray,pe as backgroundBlurriness,de as backgroundIntensity,ue as backgroundRotation,ge as batch,he as billboarding,fe as bitAnd,xe as bitNot,be as bitOr,we as bitXor,ve as bitangentGeometry,Se as bitangentLocal,Te as bitangentView,_e as bitangentWorld,Ve as bitcast,ye as blendBurn,Me as blendColor,De as blendDodge,Fe as blendOverlay,Ce as blendScreen,Ie as blur,Pe as bool,Re as buffer,Ne as bufferAttribute,Be as bumpMap,Le as burn,ke as bvec2,Ae as bvec3,Ge as bvec4,Oe as bypass,We as cache,je as call,Ue as cameraFar,ze as cameraIndex,qe as cameraNear,Ee as cameraNormalMatrix,Ze as cameraPosition,Xe as cameraProjectionMatrix,Ye as cameraProjectionMatrixInverse,He as cameraViewMatrix,Je as cameraWorldMatrix,Ke as cbrt,Qe as cdl,$e as ceil,et as checker,tt as cineonToneMapping,rt as clamp,at as clearcoat,ot as clearcoatRoughness,it as code,nt as color,st as colorSpaceToWorking,lt as colorToDirection,ct as compute,mt as computeSkinning,pt as cond,ut as context,gt as convert,ht as convertColorSpace,ft as convertToTexture,xt as cos,bt as cross,wt as cubeTexture,vt as dFdx,St as dFdy,Tt as dashSize,_t as debug,Vt as decrement,yt as decrementBefore,Mt as defaultBuildStages,Dt as defaultShaderStages,Ft as defined,Ct as degrees,It as deltaTime,Pt as densityFog,Rt as densityFogFactor,Nt as depth,Bt as depthPass,Lt as difference,kt as diffuseColor,At as directPointLight,Gt as directionToColor,Ot as dispersion,Wt as distance,jt as div,Ut as dodge,zt as dot,qt as drawIndex,Et as dynamicBufferAttribute,Zt as element,Xt as emissive,Yt as equal,Ht as equals,Jt as equirectUV,Kt as exp,Qt as exp2,$t as expression,er as faceDirection,tr as faceForward,rr as faceforward,ar as float,or as floor,ir as fog,nr as fract,sr as frameGroup,lr as frameId,cr as frontFacing,mr as fwidth,pr as gain,dr as gapSize,ur as getConstNodeType,gr as getCurrentStack,hr as getDirection,fr as getDistanceAttenuation,xr as getGeometryRoughness,br as getNormalFromDepth,wr as getParallaxCorrectNormal,vr as getRoughness,Sr as getScreenPosition,Tr as getShIrradianceAt,yr as getShadowMaterial,Mr as getShadowRenderObjectFunction,_r as getTextureIndex,Vr as getViewPosition,ba as globalId,Dr as glsl,Fr as glslFn,Cr as grayscale,Ir as greaterThan,Pr as greaterThanEqual,Rr as hash,Nr as highpModelNormalViewMatrix,Br as highpModelViewMatrix,Lr as hue,kr as increment,Ar as incrementBefore,Gr as instance,Or as instanceIndex,Wr as instancedArray,jr as instancedBufferAttribute,Ur as instancedDynamicBufferAttribute,zr as instancedMesh,qr as int,Er as inverseSqrt,Zr as inversesqrt,Xr as invocationLocalIndex,Yr as invocationSubgroupIndex,Hr as ior,Jr as iridescence,Kr as iridescenceIOR,Qr as iridescenceThickness,$r as ivec2,ea as ivec3,ta as ivec4,ra as js,aa as label,oa as length,ia as lengthSq,na as lessThan,sa as lessThanEqual,la as lightPosition,wn as lightProjectionUV,ca as lightShadowMatrix,ma as lightTargetDirection,pa as lightTargetPosition,da as lightViewPosition,ua as lightingContext,ga as lights,ha as linearDepth,fa as linearToneMapping,xa as localId,wa as log,va as log2,Sa as logarithmicDepthToViewZ,Ta as loop,_a as luminance,ya as mat2,Ma as mat3,Da as mat4,Fa as matcapUV,Ca as materialAO,Ia as materialAlphaTest,Pa as materialAnisotropy,Ra as materialAnisotropyVector,Na as materialAttenuationColor,Ba as materialAttenuationDistance,La as materialClearcoat,ka as materialClearcoatNormal,Aa as materialClearcoatRoughness,Ga as materialColor,Oa as materialDispersion,Wa as materialEmissive,ja as materialIOR,Ua as materialIridescence,za as materialIridescenceIOR,qa as materialIridescenceThickness,Ea as materialLightMap,Za as materialLineDashOffset,Xa as materialLineDashSize,Ya as materialLineGapSize,Ha as materialLineScale,Ja as materialLineWidth,Ka as materialMetalness,Qa as materialNormal,$a as materialOpacity,eo as materialPointSize,to as materialReference,ro as materialReflectivity,ao as materialRefractionRatio,oo as materialRotation,io as materialRoughness,no as materialSheen,so as materialSheenRoughness,lo as materialShininess,co as materialSpecular,mo as materialSpecularColor,po as materialSpecularIntensity,uo as materialSpecularStrength,go as materialThickness,ho as materialTransmission,fo as max,xo as maxMipLevel,Va as mediumpModelViewMatrix,bo as metalness,wo as min,vo as mix,So as mixElement,To as mod,_o as modInt,Vo as modelDirection,yo as modelNormalMatrix,Mo as modelPosition,Do as modelRadius,Fo as modelScale,Co as modelViewMatrix,Io as modelViewPosition,Po as modelViewProjection,Ro as modelWorldMatrix,No as modelWorldMatrixInverse,Bo as morphReference,Lo as mrt,ko as mul,Ao as mx_aastep,Go as mx_cell_noise_float,Oo as mx_contrast,Wo as mx_fractal_noise_float,jo as mx_fractal_noise_vec2,Uo as mx_fractal_noise_vec3,zo as mx_fractal_noise_vec4,qo as mx_hsvtorgb,Eo as mx_noise_float,Zo as mx_noise_vec3,Xo as mx_noise_vec4,Yo as mx_ramplr,Ho as mx_ramptb,Jo as mx_rgbtohsv,Ko as mx_safepower,Qo as mx_splitlr,$o as mx_splittb,ei as mx_srgb_texture_to_lin_rec709,ti as mx_transform_uv,ri as mx_worley_noise_float,ai as mx_worley_noise_vec2,oi as mx_worley_noise_vec3,ii as namespace,ni as negate,si as neutralToneMapping,li as nodeArray,ci as nodeImmutable,mi as nodeObject,pi as nodeObjects,di as nodeProxy,ui as normalFlat,gi as normalGeometry,hi as normalLocal,fi as normalMap,xi as normalView,bi as normalWorld,wi as normalize,vi as not,Si as notEqual,Ti as numWorkgroups,_i as objectDirection,Vi as objectGroup,yi as objectPosition,Mi as objectRadius,Di as objectScale,Fi as objectViewPosition,Ci as objectWorldMatrix,Ii as oneMinus,Pi as or,Ri as orthographicDepthToViewZ,Ni as oscSawtooth,Bi as oscSine,Li as oscSquare,ki as oscTriangle,Ai as output,Gi as outputStruct,Oi as overlay,Wi as overloadingFn,ji as parabola,Ui as parallaxDirection,zi as parallaxUV,qi as parameter,Ei as pass,Zi as passTexture,Xi as pcurve,Yi as perspectiveDepthToViewZ,Hi as pmremTexture,as as pointShadow,Ji as pointUV,Ki as pointWidth,Qi as positionGeometry,$i as positionLocal,en as positionPrevious,tn as positionView,rn as positionViewDirection,an as positionWorld,on as positionWorldDirection,nn as posterize,sn as pow,ln as pow2,cn as pow3,mn as pow4,pn as premult,dn as property,un as radians,gn as rand,hn as range,fn as rangeFog,xn as rangeFogFactor,bn as reciprocal,vn as reference,Sn as referenceBuffer,Tn as reflect,_n as reflectVector,Vn as reflectView,yn as reflector,Mn as refract,Dn as refractVector,Fn as refractView,Cn as reinhardToneMapping,In as remainder,Pn as remap,Rn as remapClamp,Nn as renderGroup,Bn as renderOutput,Ln as rendererReference,kn as rotate,An as rotateUV,Gn as roughness,On as round,Wn as rtt,jn as sRGBTransferEOTF,Un as sRGBTransferOETF,zn as sampler,qn as samplerComparison,En as saturate,Zn as saturation,Xn as screen,Yn as screenCoordinate,Hn as screenSize,Jn as screenUV,Kn as scriptable,Qn as scriptableValue,$n as select,es as setCurrentStack,ts as shaderStages,rs as shadow,os as shadowPositionWorld,ns as shapeCircle,is as sharedUniformGroup,ss as sheen,ls as sheenRoughness,cs as shiftLeft,ms as shiftRight,ps as shininess,ds as sign,us as sin,gs as sinc,hs as skinning,fs as smoothstep,xs as smoothstepElement,bs as specularColor,ws as specularF90,vs as spherizeUV,Ss as split,Ts as spritesheetUV,_s as sqrt,Vs as stack,ys as step,Ms as storage,Ds as storageBarrier,Fs as storageObject,Cs as storageTexture,Is as string,Ps as struct,Rs as sub,Ns as subgroupIndex,Bs as subgroupSize,Ls as tan,ks as tangentGeometry,As as tangentLocal,Gs as tangentView,Os as tangentWorld,Ws as temp,js as texture,Us as texture3D,zs as textureBarrier,qs as textureBicubic,Es as textureCubeUV,Zs as textureLoad,Xs as textureSize,Ys as textureStore,Hs as thickness,Js as time,Ks as timerDelta,Qs as timerGlobal,$s as timerLocal,el as toneMapping,tl as toneMappingExposure,rl as toonOutlinePass,al as transformDirection,ol as transformNormal,il as transformNormalToView,nl as transformedBentNormalView,sl as transformedBitangentView,ll as transformedBitangentWorld,cl as transformedClearcoatNormalView,ml as transformedNormalView,pl as transformedNormalWorld,dl as transformedTangentView,ul as transformedTangentWorld,gl as transmission,hl as transpose,fl as triNoise3D,xl as triplanarTexture,bl as triplanarTextures,wl as trunc,vl as tslFn,Sl as uint,Tl as uniform,Vl as uniformArray,_l as uniformCubeTexture,yl as uniformGroup,Ml as uniformTexture,Dl as uniforms,Fl as unpremult,Cl as userData,Il as uv,Pl as uvec2,Rl as uvec3,Nl as uvec4,Ll as varying,kl as varyingProperty,Al as vec2,Gl as vec3,Ol as vec4,Wl as vectorComponents,jl as velocity,Ul as vertexColor,zl as vertexIndex,ql as vibrance,El as viewZToLogarithmicDepth,Zl as viewZToOrthographicDepth,Xl as viewZToPerspectiveDepth,Yl as viewport,Hl as viewportBottomLeft,Jl as viewportCoordinate,Kl as viewportDepthTexture,Ql as viewportLinearDepth,$l as viewportMipTexture,ec as viewportResolution,tc as viewportSafeUV,rc as viewportSharedTexture,ac as viewportSize,oc as viewportTexture,ic as viewportTopLeft,nc as viewportUV,sc as wgsl,lc as wgslFn,cc as workgroupArray,mc as workgroupBarrier,pc as workgroupId,dc as workingToColorSpace,uc as xor};
