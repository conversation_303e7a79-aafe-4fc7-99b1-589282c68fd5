{"version": 3, "file": "SyntaxHelpers.js", "sourceRoot": "", "sources": ["../../src/analyzer/SyntaxHelpers.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AAEjC;;GAEG;AACH,MAAa,aAAa;IACxB;;;;;;;;;;;;;;;;OAgBG;IACI,MAAM,CAAC,8BAA8B,CAAC,UAAkB;QAC7D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC,CAAC,kBAAkB;QAClC,CAAC;QAED,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnD,IAAI,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,uBAAuB,CAAC,KAAa;QACjD,MAAM,KAAK,GAAa,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC;QACb,CAAC;QAED,KAAK,IAAI,CAAC,GAAW,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC9C,IAAI,IAAI,GAAW,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE,CAAC;gBAChC,2DAA2D;gBAC3D,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5B,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACZ,sDAAsD;gBACtD,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC;gBACpB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,qEAAqE;gBACrE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC;YACD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QAClB,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC;CACF;AApED,sCAoEC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport * as ts from 'typescript';\n\n/**\n * Helpers for validating various text string formats.\n */\nexport class SyntaxHelpers {\n  /**\n   * Tests whether the input string is safe to use as an ECMAScript identifier without quotes.\n   *\n   * @remarks\n   * For example:\n   *\n   * ```ts\n   * class X {\n   *   public okay: number = 1;\n   *   public \"not okay!\": number = 2;\n   * }\n   * ```\n   *\n   * A precise check is extremely complicated and highly dependent on the ECMAScript standard version\n   * and how faithfully the interpreter implements it.  To keep things simple, `isSafeUnquotedMemberIdentifier()`\n   * conservatively accepts any identifier that would be valid with ECMAScript 5, and returns false otherwise.\n   */\n  public static isSafeUnquotedMemberIdentifier(identifier: string): boolean {\n    if (identifier.length === 0) {\n      return false; // cannot be empty\n    }\n\n    if (!ts.isIdentifierStart(identifier.charCodeAt(0), ts.ScriptTarget.ES5)) {\n      return false;\n    }\n\n    for (let i: number = 1; i < identifier.length; i++) {\n      if (!ts.isIdentifierPart(identifier.charCodeAt(i), ts.ScriptTarget.ES5)) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  /**\n   * Given an arbitrary input string, return a regular TypeScript identifier name.\n   *\n   * @remarks\n   * Example input:  \"api-extractor-lib1-test\"\n   * Example output: \"apiExtractorLib1Test\"\n   */\n  public static makeCamelCaseIdentifier(input: string): string {\n    const parts: string[] = input.split(/\\W+/).filter((x) => x.length > 0);\n    if (parts.length === 0) {\n      return '_';\n    }\n\n    for (let i: number = 0; i < parts.length; ++i) {\n      let part: string = parts[i];\n      if (part.toUpperCase() === part) {\n        // Preserve existing case unless the part is all upper-case\n        part = part.toLowerCase();\n      }\n      if (i === 0) {\n        // If the first part starts with a number, prepend \"_\"\n        if (/[0-9]/.test(part.charAt(0))) {\n          part = '_' + part;\n        }\n      } else {\n        // Capitalize the first letter of each part, except for the first one\n        part = part.charAt(0).toUpperCase() + part.slice(1);\n      }\n      parts[i] = part;\n    }\n    return parts.join('');\n  }\n}\n"]}