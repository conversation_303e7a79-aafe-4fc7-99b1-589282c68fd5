{"version": 3, "file": "AstSymbolTable.js", "sourceRoot": "", "sources": ["../../src/analyzer/AstSymbolTable.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+BAA+B,CAAC,qBAAqB;AAErD,+CAAiC;AACjC,oEAAqF;AAErF,qDAAkD;AAClD,2DAAwD;AACxD,2CAAwC;AAExC,qEAAkE;AAClE,qDAAkD;AAElD,6DAA0D;AAE1D,+DAA0F;AAC1F,mDAAgD;AAChD,+EAA4E;AAiC5E;;;;;;;;;GASG;AACH,MAAa,cAAc;IAiCzB,YACE,OAAmB,EACnB,WAA2B,EAC3B,iBAAoC,EACpC,mBAAwC,EACxC,aAA4B;QA7B9B;;;;;;WAMG;QACc,wBAAmB,GAA8B,IAAI,GAAG,EAAwB,CAAC;QAElG;;WAEG;QACc,kCAA6B,GAAiC,IAAI,GAAG,EAGnF,CAAC;QAEJ,uGAAuG;QACvG,iBAAiB;QACA,oBAAe,GAAkE,IAAI,GAAG,EAGtG,CAAC;QASF,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,uBAAuB,GAAG,yCAAmB,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACtF,IAAI,CAAC,uBAAuB,GAAG,IAAI,+CAAsB,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;QAE5F,IAAI,CAAC,eAAe,GAAG,IAAI,+BAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,EAAE,mBAAmB,EAAE;YAC/F,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YAChC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;SAChD,CAAC,CAAC;QAEH,IAAI,CAAC,yBAAyB,GAAG,IAAI,GAAG,EAAU,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,gCAAgC,CAAC,UAAyB;QAC/D,OAAO,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,SAAoB;QAClD,OAAO,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;IAClE,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,UAAkB,EAAE,SAAoB;QACrE,OAAO,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,OAAO,CAAC,SAAoB;QACjC,IAAI,SAAS,YAAY,qBAAS,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,SAAS,YAAY,uCAAkB,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,2FAA2F;IACpF,4BAA4B,CAAC,IAAa,EAAE,oBAAoC;QACrF,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,mFAAmF,CAAC,CAAC;QACvG,CAAC;QAED,MAAM,mBAAmB,GAA+B,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QACD,IAAI,mBAAmB,CAAC,MAAM,KAAK,oBAAoB,EAAE,CAAC;YACxD,MAAM,IAAI,iCAAa,CAAC,8DAA8D,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,UAA6C;QACtE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,iCAAa,CAAC,4EAA4E,CAAC,CAAC;QACxG,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,MAAM,CAAC,qBAAqB,CAAC,MAAiB;QACnD,4FAA4F;QAC5F,2CAA2C;QAC3C,MAAM,mBAAmB,GAAuB,qCAAiB,CAAC,4BAA4B,CAC5F,MAAM,CAAC,WAAW,CACnB,CAAC;QACF,IAAI,mBAAmB,EAAE,CAAC;YACxB,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAED,MAAM,cAAc,GAAY,qCAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAEzF,mGAAmG;QACnG,IAAI,YAAY,GAAW,MAAM,CAAC,IAAI,CAAC;QAEvC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;YACpD,uFAAuF;YACvF,6BAA6B;YAC7B,MAAM,WAAW,GAA0B,yCAAmB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAC9F,IAAI,WAAW,EAAE,CAAC;gBAChB,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC;YAClC,CAAC;YAED,4GAA4G;YAC5G,EAAE;YACF,kBAAkB;YAClB,2BAA2B;YAC3B,qDAAqD;YACrD,QAAQ;YACR,KAAK;YACL,EAAE;YACF,aAAa;YACb,gCAAgC;YAChC,KAAK;YACL,EAAE;YACF,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,eAAe,GAAmC,EAAE,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;gBAC7F,IAAI,eAAe,IAAI,EAAE,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAAE,CAAC;oBAClE,MAAM,aAAa,GAAuB,qCAAiB,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;oBACjG,IAAI,aAAa,EAAE,CAAC;wBAClB,mGAAmG;wBACnG,uGAAuG;wBACvG,wBAAwB;wBACxB,OAAO,aAAa,CAAC;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,uFAAuF;QACvF,mEAAmE;QACnE,4FAA4F;QAC5F,IAAI,CAAC,6BAAa,CAAC,8BAA8B,CAAC,YAAY,CAAC,EAAE,CAAC;YAChE,qGAAqG;YACrG,+FAA+F;YAC/F,EAAE;YACF,cAAc;YACd,oCAAoC;YACpC,mCAAmC;YACnC,iDAAiD;YACjD,MAAM;YACN,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,0BAA0B,CAAC,kBAAsC;QACvE,IAAI,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YAChC,OAAO;QACT,CAAC;QAED,iEAAiE;QACjE,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC;QAEnC,MAAM,qBAAqB,GAA2B,IAAI,CAAC,wBAAwB,CACjF,kBAAkB,CAAC,SAAS,CAC7B,CAAC,qBAAqB,CAAC;QAExB,KAAK,MAAM,cAAc,IAAI,qBAAqB,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5D,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,SAAoB;QAC5C,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;YAC9B,mCAAmC;YACnC,SAAS,CAAC,eAAe,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,gCAAgC;QAChC,MAAM,aAAa,GAAc,SAAS,CAAC,aAAa,CAAC;QAEzD,oDAAoD;QACpD,KAAK,MAAM,cAAc,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;YAC3D,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACrE,CAAC;QAED,aAAa,CAAC,eAAe,EAAE,CAAC;QAEhC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAC1B,oGAAoG;YACpG,gGAAgG;YAChG,gBAAgB;YAChB,aAAa,CAAC,2BAA2B,CAAC,CAAC,cAA8B,EAAE,EAAE;gBAC3E,KAAK,MAAM,mBAAmB,IAAI,cAAc,CAAC,qBAAqB,EAAE,CAAC;oBACvE,yEAAyE;oBACzE,IAAI,mBAAmB,YAAY,qBAAS,EAAE,CAAC;wBAC7C,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC;4BACpC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;wBAC9C,CAAC;oBACH,CAAC;oBAED,IAAI,mBAAmB,YAAY,uCAAkB,EAAE,CAAC;wBACtD,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;4BAC9C,IAAI,CAAC,0BAA0B,CAAC,mBAAmB,CAAC,CAAC;wBACvD,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAa,EAAE,uBAAuC;QAC9E,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,qEAAqE;gBACpG,OAAO;YAET,4CAA4C;YAC5C,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,0BAA0B;YAC5D,KAAK,EAAE,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC,8CAA8C;YAC9F,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,uDAAuD;YAChG,KAAK,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,sCAAsC;gBAClE,CAAC;oBACC,gFAAgF;oBAChF,4EAA4E;oBAC5E,qCAAqC;oBACrC,MAAM,cAAc,GAA8B,qCAAiB,CAAC,kBAAkB,CACpF,IAAI,EACJ,EAAE,CAAC,UAAU,CAAC,UAAU,CACzB,CAAC;oBAEF,IAAI,cAAc,EAAE,CAAC;wBACnB,IAAI,mBAAmB,GAA0B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;wBAC1F,IAAI,CAAC,mBAAmB,EAAE,CAAC;4BACzB,MAAM,MAAM,GAA0B,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;4BAC5F,IAAI,CAAC,MAAM,EAAE,CAAC;gCACZ,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;4BAClF,CAAC;4BAED,8FAA8F;4BAC9F,+FAA+F;4BAC/F,2FAA2F;4BAC3F,2DAA2D;4BAC3D,EAAE;4BACF,sCAAsC;4BACtC,4EAA4E;4BAC5E,IAAI,eAAe,GAAY,IAAI,CAAC;4BACpC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;gCACpD,IAAI,WAAW,CAAC,aAAa,EAAE,KAAK,cAAc,CAAC,aAAa,EAAE,EAAE,CAAC;oCACnE,eAAe,GAAG,KAAK,CAAC;oCACxB,MAAM;gCACR,CAAC;4BACH,CAAC;4BAED,IAAI,eAAe,EAAE,CAAC;gCACpB,IAAI,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;oCACpE,sFAAsF;oCACtF,iGAAiG;oCACjG,2FAA2F;oCAC3F,4FAA4F;oCAC5F,4CAA4C;oCAE5C,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;wCACxC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;4CAC7D,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;4CACxD,IAAI,CAAC,cAAc,CAAC,aAAa,CAC/B,0CAA0C,cAAc,CAAC,IAAI,GAAG;gDAC9D,MAAM;gDACN,yDAA2B,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAChE,CAAC;wCACJ,CAAC;oCACH,CAAC;gCACH,CAAC;qCAAM,CAAC;oCACN,qFAAqF;oCACrF,oBAAoB;oCACpB,MAAM,IAAI,iCAAa,CAAC,gCAAgC,cAAc,CAAC,IAAI,GAAG,CAAC,CAAC;gCAClF,CAAC;4BACH,CAAC;iCAAM,CAAC;gCACN,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC,wBAAwB,CACjE,MAAM,EACN,uBAAuB,CAAC,SAAS,CAAC,UAAU,CAC7C,CAAC;gCAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;4BAChE,CAAC;wBACH,CAAC;wBAED,IAAI,mBAAmB,EAAE,CAAC;4BACxB,uBAAuB,CAAC,0BAA0B,CAAC,mBAAmB,CAAC,CAAC;wBAC1E,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,0DAA0D;YAC1D,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,CAAC;oBACC,MAAM,cAAc,GAAkB,IAAqB,CAAC;oBAC5D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;wBAC9C,MAAM,MAAM,GAA0B,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;wBAE5F,IAAI,mBAAmB,GAA0B,SAAS,CAAC;wBAE3D,IAAI,MAAM,KAAK,uBAAuB,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;4BAChE,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC;wBAC1F,CAAC;wBAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,CAAC;oBACC,MAAM,cAAc,GAAsB,IAAyB,CAAC;oBACpE,IAAI,mBAAmB,GAA0B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;oBAE1F,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;wBAC9C,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC;wBAExF,IAAI,CAAC,mBAAmB,EAAE,CAAC;4BACzB,2BAA2B;4BAC3B,MAAM,IAAI,KAAK,CAAC,iDAAiD,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;wBAChG,CAAC;wBAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;oBAChE,CAAC;oBAED,IAAI,mBAAmB,EAAE,CAAC;wBACxB,uBAAuB,CAAC,0BAA0B,CAAC,mBAAmB,CAAC,CAAC;oBAC1E,CAAC;gBACH,CAAC;gBACD,MAAM;QACV,CAAC;QAED,0CAA0C;QAC1C,MAAM,0BAA0B,GAA+B,IAAI,CAAC,oBAAoB,CACtF,IAAI,EACJ,uBAAuB,CAAC,SAAS,CAAC,UAAU,CAC7C,CAAC;QAEF,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,0BAA0B,IAAI,uBAAuB,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAEO,mBAAmB,CACzB,IAAuC,EACvC,uBAAuC;QAEvC,IAAI,mBAAmB,GAA0B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChF,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC3C,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC,0CAA0C,CACnF,IAAI,EACJ,uBAAuB,CAAC,SAAS,CAAC,UAAU,CAC7C,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,GAA0B,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAClF,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,MAAM,IAAI,KAAK,CAAC,mCAAmC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxE,CAAC;gBAED,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC,wBAAwB,CACjE,MAAM,EACN,uBAAuB,CAAC,SAAS,CAAC,UAAU,CAC7C,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAEO,oBAAoB,CAAC,IAAa,EAAE,UAAmB;QAC7D,IAAI,CAAC,+BAAc,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,MAAM,GAA0B,qCAAiB,CAAC,uBAAuB,CAC7E,IAAsB,EACtB,IAAI,CAAC,YAAY,CAClB,CAAC;QACF,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,iCAAa,CAAC,gCAAgC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,SAAS,GAA0B,IAAI,CAAC,eAAe,CAAC;YAC5D,cAAc,EAAE,MAAM;YACtB,UAAU,EAAE,UAAU;YACtB,sBAAsB,EAAE,IAAI;YAC5B,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,cAAc,GAA+B,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEhG,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,IAAI,iCAAa,CAAC,2CAA2C,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,eAAe,CAAC,OAA+B;QACrD,MAAM,cAAc,GAAc,OAAO,CAAC,cAAc,CAAC;QAEzD,sEAAsE;QACtE,MAAM,oBAAoB,GACxB,qCAAiB,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IACE,cAAc,CAAC,KAAK;YACpB,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,GAAG,EAAE,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,EACtF,CAAC;YACD,IAAI,CAAC,yCAAmB,CAAC,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC3D,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,4DAA4D;QAC5D,IAAI,qCAAiB,CAAC,SAAS,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YACnE,+FAA+F;YAC/F,2EAA2E;YAC3E,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,6BAA6B,CAAC,oBAAoB,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC;gBAC9F,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC;QAED,6DAA6D;QAC7D,IAAI,qCAAiB,CAAC,iBAAiB,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3E,4DAA4D;YAC5D,MAAM,IAAI,iCAAa,CAAC,uEAAuE,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,SAAS,GAA0B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEpF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,6DAA6D;YAC7D,IAAI,eAAe,GAAY,KAAK,CAAC;YAErC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACvB,yGAAyG;gBACzG,gGAAgG;gBAChG,mEAAmE;gBACnE,MAAM,4BAA4B,GAAW,oBAAoB,CAAC,aAAa,EAAE,CAAC,QAAQ,CAAC;gBAE3F,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,4BAA4B,CAAC,EAAE,CAAC;oBACpF,eAAe,GAAG,IAAI,CAAC;oBAEvB,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;wBACpC,OAAO,SAAS,CAAC;oBACnB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,eAAe,GAA0B,SAAS,CAAC;YAEvD,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,KAAK,MAAM,WAAW,IAAI,cAAc,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;oBAC5D,IAAI,CAAC,+BAAc,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC5D,MAAM,IAAI,iCAAa,CACrB,QAAQ,cAAc,CAAC,IAAI,gBAAgB;4BACzC,kBAAkB,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,kCAAkC;4BACnF,6BAA6B,CAChC,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,oEAAoE;gBACpE,mDAAmD;gBAEnD,+CAA+C;gBAC/C,EAAE;gBACF,wDAAwD;gBACxD,8BAA8B;gBAC9B,uCAAuC;gBACvC,wCAAwC;gBACxC,yFAAyF;gBAEzF,wFAAwF;gBACxF,IAAI,oBAAoB,EAAE,CAAC;oBACzB,MAAM,0BAA0B,GAC9B,IAAI,CAAC,iCAAiC,CAAC,oBAAoB,CAAC,CAAC;oBAE/D,IAAI,0BAA0B,EAAE,CAAC;wBAC/B,MAAM,YAAY,GAAc,qCAAiB,CAAC,uBAAuB,CACvE,0BAA4C,EAC5C,IAAI,CAAC,YAAY,CAClB,CAAC;wBAEF,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;4BACrC,cAAc,EAAE,YAAY;4BAC5B,UAAU,EAAE,OAAO,CAAC,UAAU;4BAC9B,sBAAsB,EAAE,KAAK;4BAC7B,YAAY,EAAE,IAAI;yBACnB,CAAC,CAAC;wBACH,IAAI,CAAC,eAAe,EAAE,CAAC;4BACrB,MAAM,IAAI,iCAAa,CAAC,6CAA6C,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;wBAC/F,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,SAAS,GACb,OAAO,CAAC,SAAS,IAAI,cAAc,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;YAE5E,SAAS,GAAG,IAAI,qBAAS,CAAC;gBACxB,cAAc,EAAE,cAAc;gBAC9B,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,eAAe,EAAE,eAAe;gBAChC,eAAe,EAAE,eAAe;gBAChC,aAAa,EAAE,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;aAC3E,CAAC,CAAC;YAEH,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YAExD,wEAAwE;YACxE,0CAA0C;YAC1C,KAAK,MAAM,WAAW,IAAI,cAAc,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;gBAC5D,IAAI,oBAAoB,GAA+B,SAAS,CAAC;gBACjE,IAAI,eAAe,EAAE,CAAC;oBACpB,MAAM,iBAAiB,GAAwB,IAAI,CAAC,iCAAiC,CAAC,WAAW,CAAC,CAAC;oBAEnG,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACvB,MAAM,IAAI,iCAAa,CAAC,4BAA4B,CAAC,CAAC;oBACxD,CAAC;oBAED,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;oBACjF,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC1B,MAAM,IAAI,iCAAa,CAAC,+BAA+B,CAAC,CAAC;oBAC3D,CAAC;gBACH,CAAC;gBAED,MAAM,cAAc,GAAmB,IAAI,+BAAc,CAAC;oBACxD,WAAW;oBACX,SAAS;oBACT,MAAM,EAAE,oBAAoB;iBAC7B,CAAC,CAAC;gBAEH,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;YAChD,MAAM,IAAI,iCAAa,CACrB,4BAA4B,OAAO,CAAC,UAAU,MAAM;gBAClD,eAAe,SAAS,CAAC,SAAS,uCAAuC;gBACzE,oBAAoB,SAAS,CAAC,UAAU,EAAE,CAC7C,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,iCAAiC,CAAC,IAAa;QACrD,IAAI,WAAW,GAAwB,IAAI,CAAC,MAAM,CAAC;QACnD,OAAO,WAAW,EAAE,CAAC;YACnB,IAAI,+BAAc,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3D,OAAO,WAAW,CAAC;YACrB,CAAC;YACD,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;QACnC,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAnoBD,wCAmoBC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\n/* eslint-disable no-bitwise */ // for ts.SymbolFlags\n\nimport * as ts from 'typescript';\nimport { type PackageJsonLookup, InternalError } from '@rushstack/node-core-library';\n\nimport { AstDeclaration } from './AstDeclaration';\nimport { TypeScriptHelpers } from './TypeScriptHelpers';\nimport { AstSymbol } from './AstSymbol';\nimport type { AstModule, IAstModuleExportInfo } from './AstModule';\nimport { PackageMetadataManager } from './PackageMetadataManager';\nimport { ExportAnalyzer } from './ExportAnalyzer';\nimport type { AstEntity } from './AstEntity';\nimport { AstNamespaceImport } from './AstNamespaceImport';\nimport type { MessageRouter } from '../collector/MessageRouter';\nimport { TypeScriptInternals, type IGlobalVariableAnalyzer } from './TypeScriptInternals';\nimport { SyntaxHelpers } from './SyntaxHelpers';\nimport { SourceFileLocationFormatter } from './SourceFileLocationFormatter';\n\n/**\n * Options for `AstSymbolTable._fetchAstSymbol()`\n */\nexport interface IFetchAstSymbolOptions {\n  /**\n   * The symbol after any symbol aliases have been followed using TypeScriptHelpers.followAliases()\n   */\n  followedSymbol: ts.Symbol;\n  /**\n   * True if followedSymbol is not part of the working package\n   */\n  isExternal: boolean;\n\n  /**\n   * If true, symbols with AstSymbol.nominalAnalysis=true will be returned.\n   * Otherwise `undefined` will be returned for such symbols.\n   */\n  includeNominalAnalysis: boolean;\n\n  /**\n   * True while populating the `AstSymbolTable`; false if we're doing a passive lookup\n   * without adding anything new to the table\n   */\n  addIfMissing: boolean;\n\n  /**\n   * A hint to help `_fetchAstSymbol()` determine the `AstSymbol.localName`.\n   */\n  localName?: string;\n}\n\n/**\n * AstSymbolTable is the workhorse that builds AstSymbol and AstDeclaration objects.\n * It maintains a cache of already constructed objects.  AstSymbolTable constructs\n * AstModule objects, but otherwise the state that it maintains is agnostic of\n * any particular entry point.  (For example, it does not track whether a given AstSymbol\n * is \"exported\" or not.)\n *\n * Internally, AstSymbolTable relies on ExportAnalyzer to crawl import statements and determine where symbols\n * are declared (i.e. the AstImport information needed to import them).\n */\nexport class AstSymbolTable {\n  private readonly _program: ts.Program;\n  private readonly _typeChecker: ts.TypeChecker;\n  private readonly _messageRouter: MessageRouter;\n  private readonly _globalVariableAnalyzer: IGlobalVariableAnalyzer;\n  private readonly _packageMetadataManager: PackageMetadataManager;\n  private readonly _exportAnalyzer: ExportAnalyzer;\n  private readonly _alreadyWarnedGlobalNames: Set<string>;\n\n  /**\n   * A mapping from ts.Symbol --> AstSymbol\n   * NOTE: The AstSymbol.followedSymbol will always be a lookup key, but additional keys\n   * are possible.\n   *\n   * After following type aliases, we use this map to look up the corresponding AstSymbol.\n   */\n  private readonly _astSymbolsBySymbol: Map<ts.Symbol, AstSymbol> = new Map<ts.Symbol, AstSymbol>();\n\n  /**\n   * A mapping from ts.Declaration --> AstDeclaration\n   */\n  private readonly _astDeclarationsByDeclaration: Map<ts.Node, AstDeclaration> = new Map<\n    ts.Node,\n    AstDeclaration\n  >();\n\n  // Note that this is a mapping from specific AST nodes that we analyzed, based on the underlying symbol\n  // for that node.\n  private readonly _entitiesByNode: Map<ts.Identifier | ts.ImportTypeNode, AstEntity | undefined> = new Map<\n    ts.Identifier,\n    AstEntity | undefined\n  >();\n\n  public constructor(\n    program: ts.Program,\n    typeChecker: ts.TypeChecker,\n    packageJsonLookup: PackageJsonLookup,\n    bundledPackageNames: ReadonlySet<string>,\n    messageRouter: MessageRouter\n  ) {\n    this._program = program;\n    this._typeChecker = typeChecker;\n    this._messageRouter = messageRouter;\n    this._globalVariableAnalyzer = TypeScriptInternals.getGlobalVariableAnalyzer(program);\n    this._packageMetadataManager = new PackageMetadataManager(packageJsonLookup, messageRouter);\n\n    this._exportAnalyzer = new ExportAnalyzer(this._program, this._typeChecker, bundledPackageNames, {\n      analyze: this.analyze.bind(this),\n      fetchAstSymbol: this._fetchAstSymbol.bind(this)\n    });\n\n    this._alreadyWarnedGlobalNames = new Set<string>();\n  }\n\n  /**\n   * Used to analyze an entry point that belongs to the working package.\n   */\n  public fetchAstModuleFromWorkingPackage(sourceFile: ts.SourceFile): AstModule {\n    return this._exportAnalyzer.fetchAstModuleFromSourceFile(sourceFile, undefined, false);\n  }\n\n  /**\n   * This crawls the specified entry point and collects the full set of exported AstSymbols.\n   */\n  public fetchAstModuleExportInfo(astModule: AstModule): IAstModuleExportInfo {\n    return this._exportAnalyzer.fetchAstModuleExportInfo(astModule);\n  }\n\n  /**\n   * Attempts to retrieve an export by name from the specified `AstModule`.\n   * Returns undefined if no match was found.\n   */\n  public tryGetExportOfAstModule(exportName: string, astModule: AstModule): AstEntity | undefined {\n    return this._exportAnalyzer.tryGetExportOfAstModule(exportName, astModule);\n  }\n\n  /**\n   * Ensures that AstSymbol.analyzed is true for the provided symbol.  The operation\n   * starts from the root symbol and then fills out all children of all declarations, and\n   * also calculates AstDeclaration.referencedAstSymbols for all declarations.\n   * If the symbol is not imported, any non-imported references are also analyzed.\n   *\n   * @remarks\n   * This is an expensive operation, so we only perform it for top-level exports of an\n   * the AstModule.  For example, if some code references a nested class inside\n   * a namespace from another library, we do not analyze any of that class's siblings\n   * or members.  (We do always construct its parents however, since AstDefinition.parent\n   * is immutable, and needed e.g. to calculate release tag inheritance.)\n   */\n  public analyze(astEntity: AstEntity): void {\n    if (astEntity instanceof AstSymbol) {\n      return this._analyzeAstSymbol(astEntity);\n    }\n\n    if (astEntity instanceof AstNamespaceImport) {\n      return this._analyzeAstNamespaceImport(astEntity);\n    }\n  }\n\n  /**\n   * For a given astDeclaration, this efficiently finds the child corresponding to the\n   * specified ts.Node.  It is assumed that AstDeclaration.isSupportedSyntaxKind() would return true for\n   * that node type, and that the node is an immediate child of the provided AstDeclaration.\n   */\n  // NOTE: This could be a method of AstSymbol if it had a backpointer to its AstSymbolTable.\n  public getChildAstDeclarationByNode(node: ts.Node, parentAstDeclaration: AstDeclaration): AstDeclaration {\n    if (!parentAstDeclaration.astSymbol.analyzed) {\n      throw new Error('getChildDeclarationByNode() cannot be used for an AstSymbol that was not analyzed');\n    }\n\n    const childAstDeclaration: AstDeclaration | undefined = this._astDeclarationsByDeclaration.get(node);\n    if (!childAstDeclaration) {\n      throw new Error('Child declaration not found for the specified node');\n    }\n    if (childAstDeclaration.parent !== parentAstDeclaration) {\n      throw new InternalError('The found child is not attached to the parent AstDeclaration');\n    }\n\n    return childAstDeclaration;\n  }\n\n  /**\n   * For a given ts.Identifier that is part of an AstSymbol that we analyzed, return the AstEntity that\n   * it refers to.  Returns undefined if it doesn't refer to anything interesting.\n   * @remarks\n   * Throws an Error if the ts.Identifier is not part of node tree that was analyzed.\n   */\n  public tryGetEntityForNode(identifier: ts.Identifier | ts.ImportTypeNode): AstEntity | undefined {\n    if (!this._entitiesByNode.has(identifier)) {\n      throw new InternalError('tryGetEntityForIdentifier() called for an identifier that was not analyzed');\n    }\n    return this._entitiesByNode.get(identifier);\n  }\n\n  /**\n   * Builds an AstSymbol.localName for a given ts.Symbol.  In the current implementation, the localName is\n   * a TypeScript-like expression that may be a string literal or ECMAScript symbol expression.\n   *\n   * ```ts\n   * class X {\n   *   // localName=\"identifier\"\n   *   public identifier: number = 1;\n   *   // localName=\"\\\"identifier\\\"\"\n   *   public \"quoted string!\": number = 2;\n   *   // localName=\"[MyNamespace.MySymbol]\"\n   *   public [MyNamespace.MySymbol]: number = 3;\n   * }\n   * ```\n   */\n  public static getLocalNameForSymbol(symbol: ts.Symbol): string {\n    // TypeScript binds well-known ECMAScript symbols like \"[Symbol.iterator]\" as \"__@iterator\".\n    // Decode it back into \"[Symbol.iterator]\".\n    const wellKnownSymbolName: string | undefined = TypeScriptHelpers.tryDecodeWellKnownSymbolName(\n      symbol.escapedName\n    );\n    if (wellKnownSymbolName) {\n      return wellKnownSymbolName;\n    }\n\n    const isUniqueSymbol: boolean = TypeScriptHelpers.isUniqueSymbolName(symbol.escapedName);\n\n    // We will try to obtain the name from a declaration; otherwise we'll fall back to the symbol name.\n    let unquotedName: string = symbol.name;\n\n    for (const declaration of symbol.declarations || []) {\n      // Handle cases such as \"export default class X { }\" where the symbol name is \"default\"\n      // but the local name is \"X\".\n      const localSymbol: ts.Symbol | undefined = TypeScriptInternals.tryGetLocalSymbol(declaration);\n      if (localSymbol) {\n        unquotedName = localSymbol.name;\n      }\n\n      // If it is a non-well-known symbol, then return the late-bound name.  For example, \"X.Y.z\" in this example:\n      //\n      //   namespace X {\n      //     export namespace Y {\n      //       export const z: unique symbol = Symbol(\"z\");\n      //     }\n      //  }\n      //\n      //  class C {\n      //    public [X.Y.z](): void { }\n      //  }\n      //\n      if (isUniqueSymbol) {\n        const declarationName: ts.DeclarationName | undefined = ts.getNameOfDeclaration(declaration);\n        if (declarationName && ts.isComputedPropertyName(declarationName)) {\n          const lateBoundName: string | undefined = TypeScriptHelpers.tryGetLateBoundName(declarationName);\n          if (lateBoundName) {\n            // Here the string may contain an expression such as \"[X.Y.z]\".  Names starting with \"[\" are always\n            // expressions.  If a string literal contains those characters, the code below will JSON.stringify() it\n            // to avoid a collision.\n            return lateBoundName;\n          }\n        }\n      }\n    }\n\n    // Otherwise that name may come from a quoted string or pseudonym like `__constructor`.\n    // If the string is not a safe identifier, then we must add quotes.\n    // Note that if it was quoted but did not need to be quoted, here we will remove the quotes.\n    if (!SyntaxHelpers.isSafeUnquotedMemberIdentifier(unquotedName)) {\n      // For API Extractor's purposes, a canonical form is more appropriate than trying to reflect whatever\n      // appeared in the source code.  The code is not even guaranteed to be consistent, for example:\n      //\n      //   class X {\n      //     public \"f1\"(x: string): void;\n      //     public f1(x: boolean): void;\n      //     public 'f1'(x: string | boolean): void { }\n      //   }\n      return JSON.stringify(unquotedName);\n    }\n\n    return unquotedName;\n  }\n\n  private _analyzeAstNamespaceImport(astNamespaceImport: AstNamespaceImport): void {\n    if (astNamespaceImport.analyzed) {\n      return;\n    }\n\n    // mark before actual analyzing, to handle module cyclic reexport\n    astNamespaceImport.analyzed = true;\n\n    const exportedLocalEntities: Map<string, AstEntity> = this.fetchAstModuleExportInfo(\n      astNamespaceImport.astModule\n    ).exportedLocalEntities;\n\n    for (const exportedEntity of exportedLocalEntities.values()) {\n      this.analyze(exportedEntity);\n    }\n  }\n\n  private _analyzeAstSymbol(astSymbol: AstSymbol): void {\n    if (astSymbol.analyzed) {\n      return;\n    }\n\n    if (astSymbol.nominalAnalysis) {\n      // We don't analyze nominal symbols\n      astSymbol._notifyAnalyzed();\n      return;\n    }\n\n    // Start at the root of the tree\n    const rootAstSymbol: AstSymbol = astSymbol.rootAstSymbol;\n\n    // Calculate the full child tree for each definition\n    for (const astDeclaration of rootAstSymbol.astDeclarations) {\n      this._analyzeChildTree(astDeclaration.declaration, astDeclaration);\n    }\n\n    rootAstSymbol._notifyAnalyzed();\n\n    if (!astSymbol.isExternal) {\n      // If this symbol is non-external (i.e. it belongs to the working package), then we also analyze any\n      // referencedAstSymbols that are non-external.  For example, this ensures that forgotten exports\n      // get analyzed.\n      rootAstSymbol.forEachDeclarationRecursive((astDeclaration: AstDeclaration) => {\n        for (const referencedAstEntity of astDeclaration.referencedAstEntities) {\n          // Walk up to the root of the tree, looking for any imports along the way\n          if (referencedAstEntity instanceof AstSymbol) {\n            if (!referencedAstEntity.isExternal) {\n              this._analyzeAstSymbol(referencedAstEntity);\n            }\n          }\n\n          if (referencedAstEntity instanceof AstNamespaceImport) {\n            if (!referencedAstEntity.astModule.isExternal) {\n              this._analyzeAstNamespaceImport(referencedAstEntity);\n            }\n          }\n        }\n      });\n    }\n  }\n\n  /**\n   * Used by analyze to recursively analyze the entire child tree.\n   */\n  private _analyzeChildTree(node: ts.Node, governingAstDeclaration: AstDeclaration): void {\n    switch (node.kind) {\n      case ts.SyntaxKind.JSDocComment: // Skip JSDoc comments - TS considers @param tags TypeReference nodes\n        return;\n\n      // Is this a reference to another AstSymbol?\n      case ts.SyntaxKind.TypeReference: // general type references\n      case ts.SyntaxKind.ExpressionWithTypeArguments: // special case for e.g. the \"extends\" keyword\n      case ts.SyntaxKind.ComputedPropertyName: // used for EcmaScript \"symbols\", e.g. \"[toPrimitive]\".\n      case ts.SyntaxKind.TypeQuery: // represents for \"typeof X\" as a type\n        {\n          // Sometimes the type reference will involve multiple identifiers, e.g. \"a.b.C\".\n          // In this case, we only need to worry about importing the first identifier,\n          // so do a depth-first search for it:\n          const identifierNode: ts.Identifier | undefined = TypeScriptHelpers.findFirstChildNode(\n            node,\n            ts.SyntaxKind.Identifier\n          );\n\n          if (identifierNode) {\n            let referencedAstEntity: AstEntity | undefined = this._entitiesByNode.get(identifierNode);\n            if (!referencedAstEntity) {\n              const symbol: ts.Symbol | undefined = this._typeChecker.getSymbolAtLocation(identifierNode);\n              if (!symbol) {\n                throw new Error('Symbol not found for identifier: ' + identifierNode.getText());\n              }\n\n              // Normally we expect getSymbolAtLocation() to take us to a declaration within the same source\n              // file, or else to an explicit \"import\" statement within the same source file.  But in certain\n              // situations (e.g. a global variable) the symbol will refer to a declaration in some other\n              // source file.  We'll call that case a \"displaced symbol\".\n              //\n              // For more info, see this discussion:\n              // https://github.com/microsoft/rushstack/issues/1765#issuecomment-595559849\n              let displacedSymbol: boolean = true;\n              for (const declaration of symbol.declarations || []) {\n                if (declaration.getSourceFile() === identifierNode.getSourceFile()) {\n                  displacedSymbol = false;\n                  break;\n                }\n              }\n\n              if (displacedSymbol) {\n                if (this._globalVariableAnalyzer.hasGlobalName(identifierNode.text)) {\n                  // If the displaced symbol is a global variable, then API Extractor simply ignores it.\n                  // Ambient declarations typically describe the runtime environment (provided by an API consumer),\n                  // so we don't bother analyzing them as an API contract.  (There are probably some packages\n                  // that include interesting global variables in their API, but API Extractor doesn't support\n                  // that yet; it would be a feature request.)\n\n                  if (this._messageRouter.showDiagnostics) {\n                    if (!this._alreadyWarnedGlobalNames.has(identifierNode.text)) {\n                      this._alreadyWarnedGlobalNames.add(identifierNode.text);\n                      this._messageRouter.logDiagnostic(\n                        `Ignoring reference to global variable \"${identifierNode.text}\"` +\n                          ` in ` +\n                          SourceFileLocationFormatter.formatDeclaration(identifierNode)\n                      );\n                    }\n                  }\n                } else {\n                  // If you encounter this, please report a bug with a repro.  We're interested to know\n                  // how it can occur.\n                  throw new InternalError(`Unable to follow symbol for \"${identifierNode.text}\"`);\n                }\n              } else {\n                referencedAstEntity = this._exportAnalyzer.fetchReferencedAstEntity(\n                  symbol,\n                  governingAstDeclaration.astSymbol.isExternal\n                );\n\n                this._entitiesByNode.set(identifierNode, referencedAstEntity);\n              }\n            }\n\n            if (referencedAstEntity) {\n              governingAstDeclaration._notifyReferencedAstEntity(referencedAstEntity);\n            }\n          }\n        }\n        break;\n\n      // Is this the identifier for the governingAstDeclaration?\n      case ts.SyntaxKind.Identifier:\n        {\n          const identifierNode: ts.Identifier = node as ts.Identifier;\n          if (!this._entitiesByNode.has(identifierNode)) {\n            const symbol: ts.Symbol | undefined = this._typeChecker.getSymbolAtLocation(identifierNode);\n\n            let referencedAstEntity: AstEntity | undefined = undefined;\n\n            if (symbol === governingAstDeclaration.astSymbol.followedSymbol) {\n              referencedAstEntity = this._fetchEntityForNode(identifierNode, governingAstDeclaration);\n            }\n\n            this._entitiesByNode.set(identifierNode, referencedAstEntity);\n          }\n        }\n        break;\n\n      case ts.SyntaxKind.ImportType:\n        {\n          const importTypeNode: ts.ImportTypeNode = node as ts.ImportTypeNode;\n          let referencedAstEntity: AstEntity | undefined = this._entitiesByNode.get(importTypeNode);\n\n          if (!this._entitiesByNode.has(importTypeNode)) {\n            referencedAstEntity = this._fetchEntityForNode(importTypeNode, governingAstDeclaration);\n\n            if (!referencedAstEntity) {\n              // This should never happen\n              throw new Error('Failed to fetch entity for import() type node: ' + importTypeNode.getText());\n            }\n\n            this._entitiesByNode.set(importTypeNode, referencedAstEntity);\n          }\n\n          if (referencedAstEntity) {\n            governingAstDeclaration._notifyReferencedAstEntity(referencedAstEntity);\n          }\n        }\n        break;\n    }\n\n    // Is this node declaring a new AstSymbol?\n    const newGoverningAstDeclaration: AstDeclaration | undefined = this._fetchAstDeclaration(\n      node,\n      governingAstDeclaration.astSymbol.isExternal\n    );\n\n    for (const childNode of node.getChildren()) {\n      this._analyzeChildTree(childNode, newGoverningAstDeclaration || governingAstDeclaration);\n    }\n  }\n\n  private _fetchEntityForNode(\n    node: ts.Identifier | ts.ImportTypeNode,\n    governingAstDeclaration: AstDeclaration\n  ): AstEntity | undefined {\n    let referencedAstEntity: AstEntity | undefined = this._entitiesByNode.get(node);\n    if (!referencedAstEntity) {\n      if (node.kind === ts.SyntaxKind.ImportType) {\n        referencedAstEntity = this._exportAnalyzer.fetchReferencedAstEntityFromImportTypeNode(\n          node,\n          governingAstDeclaration.astSymbol.isExternal\n        );\n      } else {\n        const symbol: ts.Symbol | undefined = this._typeChecker.getSymbolAtLocation(node);\n        if (!symbol) {\n          throw new Error('Symbol not found for identifier: ' + node.getText());\n        }\n\n        referencedAstEntity = this._exportAnalyzer.fetchReferencedAstEntity(\n          symbol,\n          governingAstDeclaration.astSymbol.isExternal\n        );\n      }\n\n      this._entitiesByNode.set(node, referencedAstEntity);\n    }\n    return referencedAstEntity;\n  }\n\n  private _fetchAstDeclaration(node: ts.Node, isExternal: boolean): AstDeclaration | undefined {\n    if (!AstDeclaration.isSupportedSyntaxKind(node.kind)) {\n      return undefined;\n    }\n\n    const symbol: ts.Symbol | undefined = TypeScriptHelpers.getSymbolForDeclaration(\n      node as ts.Declaration,\n      this._typeChecker\n    );\n    if (!symbol) {\n      throw new InternalError('Unable to find symbol for node');\n    }\n\n    const astSymbol: AstSymbol | undefined = this._fetchAstSymbol({\n      followedSymbol: symbol,\n      isExternal: isExternal,\n      includeNominalAnalysis: true,\n      addIfMissing: true\n    });\n\n    if (!astSymbol) {\n      return undefined;\n    }\n\n    const astDeclaration: AstDeclaration | undefined = this._astDeclarationsByDeclaration.get(node);\n\n    if (!astDeclaration) {\n      throw new InternalError('Unable to find constructed AstDeclaration');\n    }\n\n    return astDeclaration;\n  }\n\n  private _fetchAstSymbol(options: IFetchAstSymbolOptions): AstSymbol | undefined {\n    const followedSymbol: ts.Symbol = options.followedSymbol;\n\n    // Filter out symbols representing constructs that we don't care about\n    const arbitraryDeclaration: ts.Declaration | undefined =\n      TypeScriptHelpers.tryGetADeclaration(followedSymbol);\n    if (!arbitraryDeclaration) {\n      return undefined;\n    }\n\n    if (\n      followedSymbol.flags &\n      (ts.SymbolFlags.TypeParameter | ts.SymbolFlags.TypeLiteral | ts.SymbolFlags.Transient)\n    ) {\n      if (!TypeScriptInternals.isLateBoundSymbol(followedSymbol)) {\n        return undefined;\n      }\n    }\n\n    // API Extractor doesn't analyze ambient declarations at all\n    if (TypeScriptHelpers.isAmbient(followedSymbol, this._typeChecker)) {\n      // We make a special exemption for ambient declarations that appear in a source file containing\n      // an \"export=\" declaration that allows them to be imported as non-ambient.\n      if (!this._exportAnalyzer.isImportableAmbientSourceFile(arbitraryDeclaration.getSourceFile())) {\n        return undefined;\n      }\n    }\n\n    // Make sure followedSymbol isn't an alias for something else\n    if (TypeScriptHelpers.isFollowableAlias(followedSymbol, this._typeChecker)) {\n      // We expect the caller to have already followed any aliases\n      throw new InternalError('AstSymbolTable._fetchAstSymbol() cannot be called with a symbol alias');\n    }\n\n    let astSymbol: AstSymbol | undefined = this._astSymbolsBySymbol.get(followedSymbol);\n\n    if (!astSymbol) {\n      // None of the above lookups worked, so create a new entry...\n      let nominalAnalysis: boolean = false;\n\n      if (options.isExternal) {\n        // If the file is from an external package that does not support AEDoc, normally we ignore it completely.\n        // But in some cases (e.g. checking star exports of an external package) we need an AstSymbol to\n        // represent it, but we don't need to analyze its sibling/children.\n        const followedSymbolSourceFileName: string = arbitraryDeclaration.getSourceFile().fileName;\n\n        if (!this._packageMetadataManager.isAedocSupportedFor(followedSymbolSourceFileName)) {\n          nominalAnalysis = true;\n\n          if (!options.includeNominalAnalysis) {\n            return undefined;\n          }\n        }\n      }\n\n      let parentAstSymbol: AstSymbol | undefined = undefined;\n\n      if (!nominalAnalysis) {\n        for (const declaration of followedSymbol.declarations || []) {\n          if (!AstDeclaration.isSupportedSyntaxKind(declaration.kind)) {\n            throw new InternalError(\n              `The \"${followedSymbol.name}\" symbol has a` +\n                ` ts.SyntaxKind.${ts.SyntaxKind[declaration.kind]} declaration which is not (yet?)` +\n                ` supported by API Extractor`\n            );\n          }\n        }\n\n        // We always fetch the entire chain of parents for each declaration.\n        // (Children/siblings are only analyzed on demand.)\n\n        // Key assumptions behind this squirrely logic:\n        //\n        // IF a given symbol has two declarations D1 and D2; AND\n        // If D1 has a parent P1, then\n        // - D2 will also have a parent P2; AND\n        // - P1 and P2's symbol will be the same\n        // - but P1 and P2 may be different (e.g. merged namespaces containing merged interfaces)\n\n        // Is there a parent AstSymbol?  First we check to see if there is a parent declaration:\n        if (arbitraryDeclaration) {\n          const arbitraryParentDeclaration: ts.Node | undefined =\n            this._tryFindFirstAstDeclarationParent(arbitraryDeclaration);\n\n          if (arbitraryParentDeclaration) {\n            const parentSymbol: ts.Symbol = TypeScriptHelpers.getSymbolForDeclaration(\n              arbitraryParentDeclaration as ts.Declaration,\n              this._typeChecker\n            );\n\n            parentAstSymbol = this._fetchAstSymbol({\n              followedSymbol: parentSymbol,\n              isExternal: options.isExternal,\n              includeNominalAnalysis: false,\n              addIfMissing: true\n            });\n            if (!parentAstSymbol) {\n              throw new InternalError('Unable to construct a parent AstSymbol for ' + followedSymbol.name);\n            }\n          }\n        }\n      }\n\n      const localName: string | undefined =\n        options.localName || AstSymbolTable.getLocalNameForSymbol(followedSymbol);\n\n      astSymbol = new AstSymbol({\n        followedSymbol: followedSymbol,\n        localName: localName,\n        isExternal: options.isExternal,\n        nominalAnalysis: nominalAnalysis,\n        parentAstSymbol: parentAstSymbol,\n        rootAstSymbol: parentAstSymbol ? parentAstSymbol.rootAstSymbol : undefined\n      });\n\n      this._astSymbolsBySymbol.set(followedSymbol, astSymbol);\n\n      // Okay, now while creating the declarations we will wire them up to the\n      // their corresponding parent declarations\n      for (const declaration of followedSymbol.declarations || []) {\n        let parentAstDeclaration: AstDeclaration | undefined = undefined;\n        if (parentAstSymbol) {\n          const parentDeclaration: ts.Node | undefined = this._tryFindFirstAstDeclarationParent(declaration);\n\n          if (!parentDeclaration) {\n            throw new InternalError('Missing parent declaration');\n          }\n\n          parentAstDeclaration = this._astDeclarationsByDeclaration.get(parentDeclaration);\n          if (!parentAstDeclaration) {\n            throw new InternalError('Missing parent AstDeclaration');\n          }\n        }\n\n        const astDeclaration: AstDeclaration = new AstDeclaration({\n          declaration,\n          astSymbol,\n          parent: parentAstDeclaration\n        });\n\n        this._astDeclarationsByDeclaration.set(declaration, astDeclaration);\n      }\n    }\n\n    if (options.isExternal !== astSymbol.isExternal) {\n      throw new InternalError(\n        `Cannot assign isExternal=${options.isExternal} for` +\n          ` the symbol ${astSymbol.localName} because it was previously registered` +\n          ` with isExternal=${astSymbol.isExternal}`\n      );\n    }\n\n    return astSymbol;\n  }\n\n  /**\n   * Returns the first parent satisfying isAstDeclaration(), or undefined if none is found.\n   */\n  private _tryFindFirstAstDeclarationParent(node: ts.Node): ts.Node | undefined {\n    let currentNode: ts.Node | undefined = node.parent;\n    while (currentNode) {\n      if (AstDeclaration.isSupportedSyntaxKind(currentNode.kind)) {\n        return currentNode;\n      }\n      currentNode = currentNode.parent;\n    }\n    return undefined;\n  }\n}\n"]}