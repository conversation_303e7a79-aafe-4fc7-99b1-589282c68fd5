{"version": 3, "file": "AstSymbol.js", "sourceRoot": "", "sources": ["../../src/analyzer/AstSymbol.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAI3D,oEAA6D;AAC7D,2CAAwC;AAcxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,MAAa,SAAU,SAAQ,qBAAS;IA6DtC,YAAmB,OAA0B;QAC3C,KAAK,EAAE,CAAC;QALV,sDAAsD;QACtD,qDAAqD;QAC7C,cAAS,GAAY,KAAK,CAAC;QAKjC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAC7C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC;QACnD,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACH,IAAW,eAAe;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACH,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,wBAAwB,CAAC,cAA8B;QAC5D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,IAAI,iCAAa,CAAC,sEAAsE,CAAC,CAAC;QAClG,CAAC;QACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,IAAI,iCAAa,CAAC,iEAAiE,CAAC,CAAC;QAC7F,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,MAAgD;QACjF,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAClD,cAAc,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;CACF;AAhID,8BAgIC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport type * as ts from 'typescript';\nimport type { AstDeclaration } from './AstDeclaration';\nimport { InternalError } from '@rushstack/node-core-library';\nimport { AstEntity } from './AstEntity';\n\n/**\n * Constructor options for AstSymbol\n */\nexport interface IAstSymbolOptions {\n  readonly followedSymbol: ts.Symbol;\n  readonly localName: string;\n  readonly isExternal: boolean;\n  readonly nominalAnalysis: boolean;\n  readonly parentAstSymbol: AstSymbol | undefined;\n  readonly rootAstSymbol: AstSymbol | undefined;\n}\n\n/**\n * The AstDeclaration and AstSymbol classes are API Extractor's equivalent of the compiler's\n * ts.Declaration and ts.Symbol objects.  They are created by the `AstSymbolTable` class.\n *\n * @remarks\n * The AstSymbol represents the ts.Symbol information for an AstDeclaration.  For example,\n * if a method has 3 overloads, each overloaded signature will have its own AstDeclaration,\n * but they will all share a common AstSymbol.\n *\n * For nested definitions, the AstSymbol has a unique parent (i.e. AstSymbol.rootAstSymbol),\n * but the parent/children for each AstDeclaration may be different.  Consider this example:\n *\n * ```ts\n * export namespace N {\n *   export function f(): void { }\n * }\n *\n * export interface N {\n *   g(): void;\n * }\n * ```\n *\n * Note how the parent/child relationships are different for the symbol tree versus\n * the declaration tree, and the declaration tree has two roots:\n *\n * ```\n * AstSymbol tree:            AstDeclaration tree:\n * - N                        - N (namespace)\n *   - f                        - f\n *   - g                      - N (interface)\n *                              - g\n * ```\n */\nexport class AstSymbol extends AstEntity {\n  /** {@inheritdoc} */\n  public readonly localName: string; // abstract\n\n  /**\n   * If true, then the `followedSymbol` (i.e. original declaration) of this symbol\n   * is not part of the working package.  The working package may still export this symbol,\n   * but if so it should be emitted as an alias such as `export { X } from \"package1\";`.\n   */\n  public readonly isExternal: boolean;\n\n  /**\n   * The compiler symbol where this type was defined, after following any aliases.\n   *\n   * @remarks\n   * This is a normal form that can be reached from any symbol alias by calling\n   * `TypeScriptHelpers.followAliases()`.  It can be compared to determine whether two\n   * symbols refer to the same underlying type.\n   */\n  public readonly followedSymbol: ts.Symbol;\n\n  /**\n   * If true, then this AstSymbol represents a foreign object whose structure will be\n   * ignored.  The AstDeclaration objects will not have any parent or children, and its references\n   * will not be analyzed.\n   *\n   * Nominal symbols are tracked e.g. when they are reexported by the working package.\n   */\n  public readonly nominalAnalysis: boolean;\n\n  /**\n   * Returns the symbol of the parent of this AstSymbol, or undefined if there is no parent.\n   * @remarks\n   * If a symbol has multiple declarations, we assume (as an axiom) that their parent\n   * declarations will belong to the same symbol.  This means that the \"parent\" of a\n   * symbol is a well-defined concept.  However, the \"children\" of a symbol are not very\n   * meaningful, because different declarations may have different nested members,\n   * so we usually need to traverse declarations to find children.\n   */\n  public readonly parentAstSymbol: AstSymbol | undefined;\n\n  /**\n   * Returns the symbol of the root of the AstDeclaration hierarchy.\n   * @remarks\n   * NOTE: If this AstSymbol is the root, then rootAstSymbol will point to itself.\n   */\n  public readonly rootAstSymbol: AstSymbol;\n\n  /**\n   * Additional information that is calculated later by the `Collector`.  The actual type is `SymbolMetadata`,\n   * but we declare it as `unknown` because consumers must obtain this object by calling\n   * `Collector.fetchSymbolMetadata()`.\n   */\n  public symbolMetadata: unknown;\n\n  private readonly _astDeclarations: AstDeclaration[];\n\n  // This flag is unused if this is not the root symbol.\n  // Being \"analyzed\" is a property of the root symbol.\n  private _analyzed: boolean = false;\n\n  public constructor(options: IAstSymbolOptions) {\n    super();\n\n    this.followedSymbol = options.followedSymbol;\n    this.localName = options.localName;\n    this.isExternal = options.isExternal;\n    this.nominalAnalysis = options.nominalAnalysis;\n    this.parentAstSymbol = options.parentAstSymbol;\n    this.rootAstSymbol = options.rootAstSymbol || this;\n    this._astDeclarations = [];\n  }\n\n  /**\n   * The one or more declarations for this symbol.\n   * @remarks\n   * For example, if this symbol is a method, then the declarations might be\n   * various method overloads.  If this symbol is a namespace, then the declarations\n   * might be separate namespace blocks with the same name that get combined via\n   * declaration merging.\n   */\n  public get astDeclarations(): ReadonlyArray<AstDeclaration> {\n    return this._astDeclarations;\n  }\n\n  /**\n   * Returns true if the AstSymbolTable.analyze() was called for this object.\n   * See that function for details.\n   * @remarks\n   * AstSymbolTable.analyze() is always performed on the root AstSymbol.  This function\n   * returns true if-and-only-if the root symbol was analyzed.\n   */\n  public get analyzed(): boolean {\n    return this.rootAstSymbol._analyzed;\n  }\n\n  /**\n   * This is an internal callback used when the AstSymbolTable attaches a new\n   * AstDeclaration to this object.\n   * @internal\n   */\n  public _notifyDeclarationAttach(astDeclaration: AstDeclaration): void {\n    if (this.analyzed) {\n      throw new InternalError('_notifyDeclarationAttach() called after analysis is already complete');\n    }\n    this._astDeclarations.push(astDeclaration);\n  }\n\n  /**\n   * This is an internal callback used when the AstSymbolTable.analyze()\n   * has processed this object.\n   * @internal\n   */\n  public _notifyAnalyzed(): void {\n    if (this.parentAstSymbol) {\n      throw new InternalError('_notifyAnalyzed() called for an AstSymbol which is not the root');\n    }\n    this._analyzed = true;\n  }\n\n  /**\n   * Helper that calls AstDeclaration.forEachDeclarationRecursive() for each AstDeclaration.\n   */\n  public forEachDeclarationRecursive(action: (astDeclaration: AstDeclaration) => void): void {\n    for (const astDeclaration of this.astDeclarations) {\n      astDeclaration.forEachDeclarationRecursive(action);\n    }\n  }\n}\n"]}